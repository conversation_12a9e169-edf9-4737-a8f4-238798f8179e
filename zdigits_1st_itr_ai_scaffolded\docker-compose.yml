services:
  db:
    image: postgres:15
    container_name: erp_db
    restart: always
    environment:
      POSTGRES_USER: erp_user
      POSTGRES_PASSWORD: erp_pass
      POSTGRES_DB: erp_db
    ports:
      - "5432:5432"
    volumes:
      - pgdata:/var/lib/postgresql/data

  backend:
    build: ./backend
    container_name: erp_backend
    restart: always
    ports:
      - "4000:4000"
    environment:
      DATABASE_HOST: db
      DATABASE_PORT: 5432
      DATABASE_USER: erp_user
      DATABASE_PASSWORD: erp_pass
      DATABASE_NAME: erp_db
    depends_on:
      - db

  frontend:
    build: ./frontend
    container_name: erp_frontend
    restart: always
    ports:
      - "3000:3000"
    depends_on:
      - backend

volumes:
  pgdata:
