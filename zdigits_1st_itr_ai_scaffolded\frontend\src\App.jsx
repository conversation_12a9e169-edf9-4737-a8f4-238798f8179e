import { useState } from "react";

function App() {
  const [message, setMessage] = useState("");

  const fetchMessage = async () => {
    const res = await fetch("http://localhost:4000/api/hello");
    const text = await res.text();
    setMessage(text);
  };

  return (
    <div style={{ padding: 20 }}>
      <h1>ERP Frontend</h1>
      <button onClick={fetchMessage}>Get Backend Message</button>
      <p>{message}</p>
    </div>
  );
}

export default App;
